from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework.permissions import AllowAny
from wechatpy import parse_message, create_reply
from wechatpy.utils import check_signature
from wechatpy.exceptions import InvalidSignatureException
from django.http import HttpResponse
from django.conf import settings
from django.core.cache import cache
import requests,json

def get_token():
    AppID = settings.GZHAPPID
    AppSecret = settings.GZHAPPSECRET
    
    # 尝试从 Redis 缓存中获取 access_token
    access_token = cache.get("access_token")
    
    # 如果缓存中没有 access_token，则请求新的
    if access_token is None:
        url = f"https://api.weixin.qq.com/cgi-bin/token?grant_type=client_credential&appid={AppID}&secret={AppSecret}"
        res = requests.get(url)
        
        # 解析新的 access_token
        access_token = res.json().get('access_token')
        
        # 将新的 access_token 缓存到 Redis 中，有效期为7000秒
        cache.set("access_token", access_token, timeout=7000)
    
    return access_token


class WeChatView(APIView):
    permission_classes = [AllowAny]  # 微信接口通常对外开放，不需要认证

    def get(self, request, *args, **kwargs):
        # 微信接入验证
        token = 'xzxyq'
        signature = request.GET.get('signature', '')
        timestamp = request.GET.get('timestamp', '')
        nonce = request.GET.get('nonce', '')
        echostr = request.GET.get('echostr', '')

        try:
            check_signature(token, signature, timestamp, nonce)
            return HttpResponse(echostr, status=200)
        except InvalidSignatureException:
            return Response({"error": "Invalid signature"}, status=403)

    def post(self, request, *args, **kwargs):
        # 处理微信消息
        msg = parse_message(request.body)
        print(msg)

        # 关键词和对应的回复
        keyword_replies = {
            '新生疑惑': 'https://share.weiyun.com/LHpgzcvC',
            '学号': 'http://yx.scu.edu.cn/#/login',
        }

        # 默认回复的文本
        default_reply_text = ''

        if msg.type == 'text':
            # 获取消息内容并转小写以进行匹配
            content = msg.content.strip()
            reply_text = default_reply_text  # 初始化为默认回复

            # 检查消息内容是否包含关键词
            for keyword, response in keyword_replies.items():
                if keyword in content:
                    reply_text = response
                    break

            reply = create_reply(reply_text, msg)
        else:
            # 如果消息类型不是 'text'，返回默认回复
            reply = create_reply(default_reply_text, msg)

        return HttpResponse(reply.render(), content_type="application/xml")

def create_menu():
    access_token = get_token()
    
    # 菜单配置
    menu_data = {
        "button": [
            {
                 "type":"miniprogram",
                 "name":"小竹校友圈",
                 "url":"http://mp.weixin.qq.com",
                 "appid":"wx179988572d61c170",
                 "pagepath":"pages/index/index"
            }
        ]
    }
    
    # 发送请求创建菜单
    url = f"https://api.weixin.qq.com/cgi-bin/menu/create?access_token={access_token}"
    response = requests.post(url, data=json.dumps(menu_data, ensure_ascii=False).encode('utf-8'))
    
    # 打印返回结果
    print(response.json())