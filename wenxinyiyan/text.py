import random
from django.conf import settings
from django.core.mail import send_mail

def test():
    adjectives = [
    '沉默的', '爽快的', '心花怒放的', '悲愤的', '伤感的', '幽默的', '胆怯的', '忧郁的', '孤独的', '狂妄的',
    '自负的', '自豪的', '害羞的', '腼腆的', '忏悔的', '困惑的', '幸福的', '心寒的', '愤怒的', '激动的',
    '冷漠的', '期待的', '绝望的', '沮丧的', '骄傲的', '困倦的', '焦虑的', '平静的', '紧张的', '欣慰的',
    '怯懦的', '安心的', '满足的', '兴奋的', '沮丧的', '惊讶的', '愉悦的', '满意的', '失望的', '无助的'
]
    nouns = [
    '孔子', '庄子', '诸葛亮', '项羽', '刘邦', '秦始皇', '汉武帝', '唐太宗', '李白', '杜甫', '王羲之', '苏轼',
    '柏拉图', '亚里士多德','苏格拉底'
    '爱因斯坦', '牛顿', '达芬奇', '莎士比亚', '贝多芬',  '居里夫人', '丘吉尔', '甘地', '林肯',
    '阿基米德', '哥白尼', '开普勒', '伽利略', '牛顿', '拉瓦锡', '法拉第', '麦克斯韦', '爱因斯坦', '居里夫人',
    '达尔文',   '玻尔', '霍金', '费曼', '乔布斯', '霍金', '梵高'
    ]
    
    adjective = random.choice(adjectives)
    noun = random.choice(nouns)
        # 测试生成随机名称
    print(adjective + noun)
    return adjective + noun

def send_report_email(post_id=1):
    subject = '帖子举报'
    message = f' {post_id} 号帖子被举报.'
    from_email = settings.DEFAULT_FROM_EMAIL
    recipient_list = ['<EMAIL>']  # 接收通知的邮箱列表
    
    send_mail(subject, message, from_email, recipient_list)
    

if __name__ == '__main__':
    send_report_email()
