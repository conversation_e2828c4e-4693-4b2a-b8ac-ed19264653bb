
# -*- coding: utf-8 -*-
import requests
import json


################————————模型配置————————######################
API_KEY = "bce-v3/ALTAK-5cJYeX1mVIRgtt0nuhik5/8574e775f4b7ee0df52bbc37a4ff25a70ff9e99a"
MODEL = "ernie-4.5-8k-preview"
############################################################

import logging

# 配置日志记录
audit_logger = logging.getLogger("blog.audit")

        
def log_audit_record(content, class_id, recommend, reason):
    """记录审核日志"""
    log_message = (
        f"帖子内容: {content}\n"
        f"分类: {class_id}, 是否封禁: {recommend}, 原因: {reason}\n"
        f"------------------------"
    )
    # print("写入log")
    audit_logger.info(log_message)        
        
def contentProcess(content):
    if len(content) > 500:
        content = content[:250] + content[-250:]
    return content

def wenxinyiyanAPI( content ="你好你是谁？"):
    content = contentProcess(content)
    url = "https://qianfan.baidubce.com/v2/chat/completions"
    
    payload = json.dumps({
        "model": MODEL,
        "messages": [
            {
                "role": "system",
                "content": "你现在是“小竹校友圈”的“小竹学长”，你每次回答的内容都只有一句话，不允许分点讨论。这是一个为同学服务的论坛小程序，基本上所有的问题都是校园内部的问题，请以一个学生角度去回答！凌晨12:00-5:00为匿名论坛，有评分专区，大家可以在这里随意打分，还有资料库专区，这里可以用小竹笋兑换资料，小竹笋的获取方式有三种，分别为：1，签到，2，抽奖，3被点赞或者被评论。发帖的人都是你四川大学的学弟学妹,平时在学校生活，你平时幽默风趣，青春阳光、温柔善良，乐观积极，很会安慰人.你会首先分析内容，提取关键词和情感倾向，然后生成50字左右的合适的评论，你的评论有深度但又轻快有趣，善用~结尾。你不用语气词开头，比如“哎呀”，“哈哈”。你的回答字数禁止超过50字!不允许超过！！"
            },
            {
                "role": "user",
                "content": content
            }
        ],
        "temperature":0.8,
        "max_output_tokens": 60,
    })
    headers = {
        'Content-Type': 'application/json',
        'Authorization': 'Bearer bce-v3/ALTAK-5cJYeX1mVIRgtt0nuhik5/8574e775f4b7ee0df52bbc37a4ff25a70ff9e99a',
        'appid': 'app-lLjKEoNT'
    }
    
    response = requests.request("POST", url, headers=headers, data=payload)
    
    data = json.loads(response.text)  
    message = data['choices'][0]['message']
    content = message.get('content', '')
    # print(content)
    
    return content
    

def recommendAPI( content ="你是笨蛋"):
    content_concat = contentProcess(content)
    content_concat = "这是一条新帖子的内容，请帮我分类并判断是否适合推荐，以{\"class\": \"2\",\"recommend\": 1,\"reason\": \"\"}类型的格式回复我：" + content
    url = "https://qianfan.baidubce.com/v2/chat/completions"
    
    payload = json.dumps({
        "model": MODEL,
        "messages": [
            {
                "role": "system",
                "content": "你现在是小竹校友圈校园论坛的管理专家。下面是一条新帖子的内容，现在对用户发布的内容进行分类属于哪类，一共有几种类别[1代表心情感受类帖子，这类帖子是大家分想对校园生活的感受和情绪；2代表万能求助类帖子，这类帖子是在生活中遇到困难，想询问大家；3代表二手闲置，这类帖子属于大家有二手的东西相互交易；4是代表教程经验类，分享生活经验或者技巧方法等。5代表招聘广告类，比如找人兼职，包括但不限于家教，发传单，线上兼职，剪辑视频等，让人加他的联系方式等情况；6代表营销广告类，这类广告包括但不限于卖电话卡，推荐驾校，宣传雅思托福培训，以及留学等商业服务等广告；7代表色情类，包括但不限于约炮，找一日伴侣等隐晦的性暗示的东西；8代表政治类，设计针对国家的领导人等政治元素谈论；9代表其他种类]。同时，你需要分辨此条帖子内容是否应该被封禁，不应过于严厉，返回0代表不适合展示应封禁，1代表适合展示不封禁。所以你的格式应该包括两个方面，一个是类别属性，一个是是否适合展示，广告、色情、政治以及其他不适合展示的内容，例如：{\"class\": \"2\",\"recommend\": 1,\"reason\": \"\"},不需要其他额外的回复"
            },
            {
                "role": "user",
                "content": content_concat
            }
        ],
        "temperature":0.2,
        "stop": [
            "}"
        ],
        "max_output_tokens": 100,
        "response_format": {
            "type": "json_object"  # 确保 response_format 是一个对象，而不是字符串
        }
    })
    headers = {
        'Content-Type': 'application/json',
        'Authorization': 'Bearer bce-v3/ALTAK-5cJYeX1mVIRgtt0nuhik5/8574e775f4b7ee0df52bbc37a4ff25a70ff9e99a',
        'appid': 'app-lLjKEoNT'
    }
    
    response = requests.request("POST", url, headers=headers, data=payload)

    # 检查请求是否成功
    if response.status_code == 200:
        try:
            data = response.json()  # 解析外层 JSON
            
            # 打印完整的数据结构以调试
            print("Full Response Data:", data)
            
            # 检查是否存在 'choices' 列表
            if 'choices' in data and len(data['choices']) > 0:
                # 获取第一个 choice 中的 message 内容
                message = data['choices'][0]['message']
                data_msg = message.get('content', '')
                print("Content:", data_msg)
                # 提取 JSON 部分（去除 markdown 代码块标记）
                json_str = data_msg.strip()
                if json_str.startswith('```json'):
                    json_str = json_str[7:].strip()  # 提取 JSON 内容
                    
                    try:
                        # 解析内层 JSON
                        result = json.loads(json_str)
                        post_class = result.get('class')
                        post_recommend = result.get('recommend')
                        reason = result.get('reason')
                        print("审核结果：",post_class, post_recommend,reason)
                        # 记录日志
                        log_audit_record(content.split(": ")[-1], post_class, post_recommend, reason)
                        return post_class, post_recommend
                    except json.JSONDecodeError:
                        print("Error: Failed to parse inner JSON")
                        print("Inner JSON string:", json_str)
                elif json_str.startswith('{'):
                    try:
                        # 解析内层 JSON
                        result = json.loads(json_str)
                        post_class = result.get('class')
                        post_recommend = result.get('recommend')
                        reason = result.get('reason')
                        print("审核结果：",post_class, post_recommend,reason)
                        # 记录日志
                        log_audit_record(content.split(": ")[-1], post_class, post_recommend, reason)
                        return post_class, post_recommend
                    except json.JSONDecodeError:
                        print("Error: Failed to parse inner JSON")
                        print("Inner JSON string:", json_str)
                else:
                    print("Error: Content does not contain valid JSON code block")
            else:
                print("Error: 'choices' not found in response data or empty")
                
        except json.JSONDecodeError:
            print("Error: Failed to parse outer JSON response")
    
        # 默认返回值（可根据需求调整）
        return 9, 0
    else:
        print(f"Error: Request failed with status code {response.status_code}")



def actrecommendAPI( content ="请问怎么约"):
    content_concat = contentProcess(content)
    content_concat = "这是一条找搭子的内容，请帮我分类并判断是否应该被封禁，以{\"class\": \"2\",\"recommend\": 1,\"reason\": \"\"}类型的格式回复我：" + content
    url = "https://qianfan.baidubce.com/v2/chat/completions"
    
    payload = json.dumps({
        "model": MODEL,
        "messages": [
            {
                "role": "system",
                "content": '''
                    你现在是「小竹校友圈」校园论坛的智能审核系统，需对用户发布的 **「找搭子」类帖子 ** 进行分类和合规性判断。以下是审核规则：
                    违规判断与封禁规则:
                    必须封禁（返回 recommend=0）的情况：
                    1,色情或性暗示：
                    包含约炮、隐晦性邀请（如「深夜独处求陪伴」「私密空间搭子」）、暴露性器官描述等。
                    例：「找夜聊搭子，可视频裸聊」→ 分类 1，封禁。
                    2,恶意引流或营销广告：
                    以「找搭子」为名引导添加微信 / QQ / 其他平台账号，或推销商品 / 服务（如代购、课程推广）。
                    例：「找探店搭子，加微信领优惠券」→ 分类 2，封禁。
                    3,政治敏感内容：
                    借搭子话题讨论敏感政治事件、抹黑国家领导人或煽动对立。
                    例：「找政治讨论搭子，聊聊某事件真相」→ 分类 3，封禁。
                    4,危险或违法活动：
                    涉及线下约见的危险违法行为、违法活动（如代购管制药品）。
                    例：「找搭子一起偷东西」→ 分类 4，封禁，reason=「危险行为」。
                    
                    允许展示（返回 recommend=1）的情况：
                    5,正常社交需求：明确且健康的搭子类型（如学习、运动、社团活动、考试组队等），无违规信息。
                    例：「找图书馆自习搭子，每天上午 9 点 - 12 点」→ 分类 5，允许展示，reason=「找搭子 - 学习」。
                    6,中性内容：无明确违规，但不属于原有 1-8 类的普通搭子信息。
                    例：「找饭搭子，学校食堂 AA 制」→ 分类 6，允许展示，reason=「找搭子 - 生活」。
                    
                    三、输出格式要求
                    请严格按以下 JSON 格式返回结果，无需额外文字说明：

                    json
                    {  
                    "class": "分类编号（1-9）",  
                    "recommend": "0（封禁）或 1（允许展示）",  
                    "reason": "必填，需说明分类依据或违规类型（如「找搭子-运动」「恶意引流」）"  
                    }  

                    示例参考
                    "找跑步搭子，每周三次田径场"	5	1	找搭子 - 运动
                    "找兼职搭子，加微信详聊"	2	0	恶意引流 - 兼职广告
                    "找同学讨论某政治事件真相"	3	0	政治敏感内容
                '''            
            },
            {
                "role": "user",
                "content": content_concat
            }
        ],
        "temperature":0.2,
        "stop": [
            "}"
        ],
        "max_output_tokens": 100,
        "response_format": {
            "type": "json_object"  # 确保 response_format 是一个对象，而不是字符串
        }
        })
    headers = {
        'Content-Type': 'application/json',
        'Authorization': 'Bearer bce-v3/ALTAK-5cJYeX1mVIRgtt0nuhik5/8574e775f4b7ee0df52bbc37a4ff25a70ff9e99a',
        'appid': 'app-lLjKEoNT'
    }
    
    response = requests.request("POST", url, headers=headers, data=payload)
    print(response.text)
    # 检查请求是否成功
    if response.status_code == 200:
        try:
            data = response.json()  # 解析外层 JSON
            
            # 打印完整的数据结构以调试
            # print("Full Response Data:", data)
            
            # 检查是否存在 'choices' 列表
            if 'choices' in data and len(data['choices']) > 0:
                # 获取第一个 choice 中的 message 内容
                message = data['choices'][0]['message']
                data_msg = message.get('content', '')
                # print("Content:", data_msg)
                # 提取 JSON 部分（去除 markdown 代码块标记）
                json_str = data_msg.strip()
                if json_str.startswith('```json'):
                    json_str = json_str[7:].strip()  # 提取 JSON 内容
                    
                    try:
                        # 解析内层 JSON
                        result = json.loads(json_str)
                        post_class = result.get('class')
                        post_recommend = result.get('recommend')
                        reason = result.get('reason')
                        # print(post_class, post_recommend)
                        # 记录日志
                        log_audit_record(content.split(": ")[-1], post_class, post_recommend, reason)
                        return post_class, post_recommend
                    except json.JSONDecodeError:
                        print("Error: Failed to parse inner JSON")
                        print("Inner JSON string:", json_str)
                else:
                    print("Error: Content does not contain valid JSON code block")
            else:
                print("Error: 'choices' not found in response data or empty")
                
        except json.JSONDecodeError:
            print("Error: Failed to parse outer JSON response")
    
        # 默认返回值（可根据需求调整）
        return 9, 0
    else:
        print(f"Error: Request failed with status code {response.status_code}")


if __name__ == '__main__':
    recommendAPI()

