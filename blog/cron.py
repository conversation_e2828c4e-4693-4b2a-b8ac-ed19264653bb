# blog/cron.py
import pytz
import traceback
import requests
import json
from .models import *
from datetime import datetime, timedelta
from django.utils import timezone
from django.conf import settings
from django.core.cache import cache





def DeleteNightPostsCronJob():
    from .models import Post  # 将导入语句放在函数内部
    from django.utils import timezone

    try:
        # 获取当前 UTC 时间
        now_utc = timezone.now()

        # 使用项目的时区设置
        local_tz = pytz.timezone('Asia/Shanghai')  # 根据项目实际时区设置

        # 将 UTC 时间转换为当地时间
        now_local = now_utc.astimezone(local_tz)

        # 计算昨天22:00的时间
        yesterday_evening = now_local.replace(hour=22, minute=0, second=0, microsecond=0) - timedelta(days=1)

        # 计算今天5:00的时间
        today_morning = now_local.replace(hour=5, minute=0, second=0, microsecond=0)
                
        print(yesterday_evening)
        print(today_morning)
    
            
        # 执行删除操作并获取删除的数量
        deleted_count, _ = Post.objects.filter(created_at__gte=yesterday_evening, created_at__lt=today_morning).delete()
        
        # 打印删除的帖子数量
        print(f"启动抹去，删除了 {deleted_count} 条帖子")

    except Exception as e:
        # 捕获并打印错误信息
        print("执行删除操作时发生错误：")
        print(str(e))
        # 打印完整的堆栈跟踪信息以帮助调试
        print(traceback.format_exc())

def get_access_token():
    try:
        access_token = cache.get('access_token')
        
        if not access_token:
            url = f"https://api.weixin.qq.com/cgi-bin/token?grant_type=client_credential&appid={settings.APPID}&secret={settings.APPSECRET}"
            response = requests.get(url)
            result = response.json()

            if result.get('errcode'):
                raise Exception(f"Error getting access_token: {result.get('errmsg')}")
            
            access_token = result.get('access_token')
            cache.set('access_token', access_token, timeout=7000)  # 设置缓存时间为7000秒
        
        return access_token
    except Exception as e:
        print(f"Error in get_access_token: {str(e)}")
        raise

# 方法：生成短链接
def generate_short_link(page_url, page_title=None, is_permanent=False):
    try:
        access_token = get_access_token()  # 获取 access_token 的函数
        url = f"https://api.weixin.qq.com/wxa/genwxashortlink?access_token={access_token}"

        data = {
            "page_url": page_url,
            "is_permanent": is_permanent
        }

        if page_title:
            data["page_title"] = page_title

        headers = {
            'Content-Type': 'application/json'
        }
        
        
        # 使用 json 参数来自动设置 Content-Type 头
        response = requests.post(url, json=data, headers=headers)
        
        result = response.json()

        if result.get('errcode') == 0:
            return result.get('link')
        else:
            raise Exception(f"Error generating short link: {result.get('errmsg')}")
    except Exception as e:
        print(f"Error in generate_short_link: {str(e)}")
        raise

def fetch_and_process_posts():
    try:
        
        now = timezone.now()
        print("生成链接",now)
        ten_minutes_ago = now - timedelta(minutes=10)
        
        posts = Post.objects.filter(created_at__gte=ten_minutes_ago)

        results = []
        for post in posts:
            page_url = f"pages/detail/detail?postid={post.id}"
            try:
                short_link = generate_short_link(page_url, '', False)
                short_link_code = short_link.split('/')[-1]
                formatted_output = f"{post.content[:14]}\nmp://{short_link_code}"
                results.append(formatted_output)
            except Exception as e:
                print(f"Error processing post ID {post.id}: {str(e)}")

        # Combine results with a space between each
        combined_results = "\n\n".join(results)

        # Log or handle the combined_results as needed
     
        print(combined_results)  # 这里只是示例，实际中可以写入文件或数据库等     
    except Exception as e:
        print(f"Error in fetch_and_process_posts: {str(e)}")
        raise


if __name__ == '__main__':
    fetch_and_process_posts()
        