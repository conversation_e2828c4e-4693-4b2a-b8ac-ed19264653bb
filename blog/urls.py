from django.urls import path,include
from .views import *
from . import views
from rest_framework.routers import DefaultRouter

router = DefaultRouter()
router.register(r'activity', GroupActivityViewSet, basename='activity')

urlpatterns = [
    path('posts/', PostListCreateView.as_view(), name='post-list-create'),
    path('posts/<int:pk>/', PostRetrieveUpdateDestroyView.as_view(), name='post-detail'),
    path('toppost/', TopPostView.as_view(), name='top-post'),
    path('login/',WeixinLogin.as_view()),
    path('authImgUpload/', AuthImgUploadView.as_view(), name='authImgUpload'),
    path('userInfo/',Userinfo.as_view()),
    path('data/', UserData.as_view(), name='data'),
    path('postCreate/', PostCreateView.as_view(), name='create'),
    path('post_report/', PostRepotView.as_view(), name='post_report'),
    path('commentCreate/', CommentCreateView.as_view(), name='commentCreate'),
    path('subCommentCreate/', SubCommentCreateView.as_view(), name='subCommentCreate'),
    path('imgUpload/', ImgUploadView.as_view(), name='imgUpload'),
    path('actimgUpload/', ActImgUploadView.as_view(), name='imgUpload'),
    path('likePost/<int:postid>/', LikePostView.as_view(), name='likePost'),
    path('likeComment/<int:commentid>/', LikeCommentView.as_view(), name='likeComment'),
    path('LikeTargetComment/<int:commentid>/', LikeTargetCommentView.as_view(), name='likeTargetComment'),
    path('LikeTargetSubcomment/<int:subcommentid>/', LikeTargetSubcommentView.as_view(), name='likeTargetSubcomment'),
    path('hot_posts/', HotPostsView.as_view(), name='hot-posts'),
    path('timeTable/', TimeTableView.as_view(), name='time-table'),
    path('score_targets/', ScoreTargetListView.as_view(), name='score-targets-list'),
    path('score_targets_first/', ScoreTargetFirstView.as_view(), name='score-targets-first'),
    path('score_targets/<int:id>/', ScoreTargetView.as_view(), name='score-targets-detail'),
    path('scoreTargetCreate/', ScoreTargetCreateView.as_view(), name='scoreTargetCreate'),
    path('ScoreTargetImgUpload/', ScoreTargetImgUploadView.as_view(), name='ScoreTargetImgUpload'),
    path('MakeScore/', MakeScoreView.as_view(), name='MakeScore'),
    path('MakeScoreImgUpload/', MakeScoreImgUploadView.as_view(), name='MakeScoreImgUpload'),
    path('ScoreCommentCreate/', ScoreCommentCreateView.as_view(), name='ScoreCommentCreate'),  
    path('ScoreTargetSearch/', views.target_search, name='target_search'), 
    path('PostSearch/', views.post_search, name='post_search'), 
    path('CheckIn/',CheckInView.as_view(),name='check_in'),
    path('RecentCheckIns/', RecentCheckInsAPIView.as_view(), name='recent_checkins_api'),
    path('BambooRank/', BambooRankAPIView.as_view(), name='bamboo-rank'),
    path('my_posts/', MyPostsAPIView.as_view(), name='my_posts'),
    path('book_library/', FreeBookListView.as_view(), name='book_library'),
    path('BookSearch/', views.book_search, name='book_search'),
    path('book_purchase/', FreeBookPurchaseView.as_view(), name='book_purchase'),
    path('lottery/', LotteryAPIView.as_view(), name='lottery'),
    path('notifications/', NotificationListView.as_view(), name='get_notification'),
    path('notifications/<int:pk>/mark_as_read/', NotificationMarkAsReadView.as_view(), name='update_notification'),
    path('fetchPosts/', FetchPostsView.as_view(), name='fetch-posts'),
    path('qqfetchPosts/', QQFetchPostsView.as_view(), name='qq-fetch-posts'),
    path('posts/<int:postid>/previous/', PreviousPostView.as_view(), name='previous-post'),
    path('actcommentCreate/', ActCommentCreateView.as_view(), name='actcommentCreate'),
    path('actsubCommentCreate/', ActSubCommentCreateView.as_view(), name='actsubCommentCreate'),
    path('', include(router.urls)),
    path('getallnum/',views.get_total_num_by_id,name='getallnum'),
    path('actlist/', GroupActivityListView.as_view(), name='act-list'),
    path('act/<int:actid>/', GroupActivityView.as_view(), name='act-detail'),
    path('qrcode/',WechatGroupQrcodeViewSet.as_view(),name="qrcode"),
    path('current_lottery_activity/', CurrentLotteryActivityView.as_view(), name='current_lottery_activity'),
    path('lottery_activity/', LotteryActivityView.as_view(), name='lottery_activity'),
]