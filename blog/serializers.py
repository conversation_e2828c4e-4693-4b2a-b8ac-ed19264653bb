# posts/serializers.py
from rest_framework import serializers
from django.contrib.auth.models import User
from django.db.models import Count
from django.utils import timezone
from .models import *
import datetime
from datetime import time
import random
from miniprogram.settings import MYHOST

class UserSerializer(serializers.ModelSerializer):
    nickname = serializers.SerializerMethodField()
    avatar = serializers.SerializerMethodField()

    class Meta:
        model = WXuser
        fields = '__all__'

    def get_nickname(self, obj):
        return obj.nickname

    def get_avatar(self, obj):
        if obj.avatar:
            return f'{MYHOST}{obj.avatar.url}'
        return f'{MYHOST}/media/avatars/smile.png'  # 默认头像图片的 URL


class RandomUserSerializer(serializers.ModelSerializer):
    nickname = serializers.SerializerMethodField()
    avatar = serializers.SerializerMethodField()

    class Meta:
        model = WXuser
        fields = '__all__'

    def get_nickname(self, obj):
        # 当前的本地时间
        now_local = timezone.localtime(timezone.now())
        # 计算昨天的 22:00 和今天的 5:00
        yesterday_evening = now_local.replace(hour=22, minute=0, second=0, microsecond=0)
        today_morning = now_local.replace(hour=5, minute=0, second=0, microsecond=0)
        # 检查当前时间是否在昨天22:00到今天5:00之间
        if yesterday_evening <= now_local or now_local <= today_morning:
            return obj.randomname
        return obj.nickname

    def get_avatar(self, obj):
        # 当前的本地时间
        now_local = timezone.localtime(timezone.now())

        # 计算昨天的 22:00 和今天的 5:00
        yesterday_evening = now_local.replace(hour=22, minute=0, second=0, microsecond=0)
        today_morning = now_local.replace(hour=5, minute=0, second=0, microsecond=0)

        # 如果当前时间在昨天 22:00 到今天 5:00 之间，选择随机头像
        if yesterday_evening <= now_local or now_local <= today_morning:
            random_avatar = random.choice([
                'random5.jpg',
                'random4.jpg'
            ])
            random_avatar_url = f'{MYHOST}/media/random-avatars/{random_avatar}'
            return random_avatar_url
        if obj.avatar:
            return f'{MYHOST}{obj.avatar.url}'
        return f'{MYHOST}/media/avatars/smile.png'  # 默认头像图片的 URL


class SubCommentSerializer(serializers.ModelSerializer):
    commenter = RandomUserSerializer()
    replyUser = RandomUserSerializer()

    class Meta:
        model = SubComment
        fields = '__all__'

class CommentStandardSerializer(serializers.ModelSerializer):
    commenter = RandomUserSerializer()
    subComments = SubCommentSerializer(many=True)

    class Meta:
        model = Comment
        fields = '__all__'

class CommentSerializer(serializers.ModelSerializer):
    commenter = RandomUserSerializer()
    subComments = SubCommentSerializer(many=True)
    likes_count = serializers.SerializerMethodField()
    is_liked_by_user = serializers.SerializerMethodField()  # 新增字段    

    class Meta:
        model = Comment
        fields = '__all__'
        
    def get_likes_count(self, obj):
        return CommentLike.objects.filter(comment=obj).count()
    

    def get_is_liked_by_user(self, obj):
         # 获取请求的用户
        user = self.context['request'].user  
        if not user or not user.is_authenticated:
            return False
        return CommentLike.objects.filter(comment=obj, user=user.wxuser).exists()

class PostHashTagSerializer(serializers.ModelSerializer):
    class Meta:
        model = PostHashTag
        fields = '__all__'  # 包含所有字段     
    
class PostSerializer(serializers.ModelSerializer):
    poster = RandomUserSerializer()  # 嵌套使用 UserSerializer 来序列化帖子作者
    comments = CommentSerializer(many=True)  # 通过 CommentSerializer 序列化评论
    post_hashtag = PostHashTagSerializer()
    likes_count = serializers.SerializerMethodField()
    is_liked_by_user = serializers.SerializerMethodField()  # 新增字段
    is_user_self = serializers.SerializerMethodField()  # 新增字段

    class Meta:
        model = Post
        fields = '__all__'  # 包含所有字段

    def get_likes_count(self, obj):
        return Like.objects.filter(post=obj).count()

    def get_is_liked_by_user(self, obj):
        user = self.context['request'].user
        if not user or not user.is_authenticated:
            return False
        try:
            wx_user = user.wxuser  # 尝试获取用户的 wxuser 对象
        except AttributeError:
            return False
        return Like.objects.filter(post=obj, user=wx_user).exists()

    def get_is_user_self(self, obj):
        user = self.context['request'].user
        if not user or not user.is_authenticated:
            return False
        try:
            wx_user = user.wxuser  # 尝试获取用户的 wxuser 对象
        except AttributeError:
            return False
        return obj.poster == wx_user
    
    def get_comments(self, obj):
        # 在这里，我们创建了CommentSerializer的实例并显式传递了上下文
        serializer = CommentSerializer(obj.comments.all(), many=True, context=self.context)
        return serializer.data

    def to_representation(self, instance):
        # 每次序列化帖子时，阅读量加1
        instance.read_count += 1
        instance.save(update_fields=['read_count'])
        return super().to_representation(instance)

class PostSerializer2(serializers.ModelSerializer):
    poster = RandomUserSerializer()  # 嵌套使用 UserSerializer 来序列化帖子作者
    post_hashtag = PostHashTagSerializer()
    # comments = CommentSerializer(many=True)  # 通过 CommentSerializer 序列化评论

    class Meta:
        model = Post
        fields = '__all__'  # 包含所有字段
        
    def to_representation(self, instance):
        # 每次序列化帖子时，阅读量加1
        instance.read_count += 1
        instance.save(update_fields=['read_count'])
        instance.point_count += 1
        instance.save(update_fields=['point_count'])
        return super().to_representation(instance)

        


class HashTagSerializer(serializers.ModelSerializer):
    class Meta:
        model = HashTag
        fields = '__all__'  # 包含所有字段 

class ScoreSubCommentSerializer(serializers.ModelSerializer):
    likes_count = serializers.SerializerMethodField()
    commenter = UserSerializer()
    is_liked_by_user = serializers.SerializerMethodField()

    class Meta:
        model = ScoreSubComment
        fields = '__all__'
        
    def get_likes_count(self, obj):
        return ScoreSubCommentLike.objects.filter(subcomment=obj).count()

    def get_is_liked_by_user(self, obj):
        user = self.context['request'].user
        return ScoreSubCommentLike.objects.filter(subcomment=obj, user=user.wxuser).exists()

class ScoreSubCommentAnyUserSerializer(serializers.ModelSerializer):
    likes_count = serializers.SerializerMethodField()
    commenter = UserSerializer()


    class Meta:
        model = ScoreSubComment
        fields = '__all__'
        
    def get_likes_count(self, obj):
        return ScoreSubCommentLike.objects.filter(subcomment=obj).count()

           
class ScoreCommentSerializer(serializers.ModelSerializer):
    likes_count = serializers.SerializerMethodField()
    user = UserSerializer()
    scoreSubComments = serializers.SerializerMethodField()
    is_liked_by_user = serializers.SerializerMethodField()

    class Meta:
        model = ScoreComment
        fields = '__all__'
    
    def get_scoreSubComments(self, obj):
        # 获取并排序评论
        score_subcomments = obj.scoreSubComments.annotate(like_count=Count('liked_by')).order_by('-like_count', '-created_at')
        serializer = ScoreSubCommentSerializer(instance=score_subcomments, many=True, context=self.context)
        return serializer.data    
    
    def get_likes_count(self, obj):
        return ScoreCommentLike.objects.filter(score_comment=obj).count()

    def get_is_liked_by_user(self, obj):
        user = self.context['request'].user
        return ScoreCommentLike.objects.filter(score_comment=obj, user=user.wxuser).exists()  

         
class ScoreCommentAnyUserSerializer(serializers.ModelSerializer):
    likes_count = serializers.SerializerMethodField()
    user = UserSerializer()
    scoreSubComments= ScoreSubCommentAnyUserSerializer(many=True)


    class Meta:
        model = ScoreComment
        fields = '__all__'
        
    def get_likes_count(self, obj):
        return ScoreCommentLike.objects.filter(score_comment=obj).count()



class ScoreTargetSerializer(serializers.ModelSerializer):
    creater = UserSerializer()
    hashtag = HashTagSerializer()
    score_comments = serializers.SerializerMethodField()
    
    class Meta:
        model = ScoreTarget
        fields = '__all__'

    def get_score_comments(self, obj):
        # 获取热度最高的前三条评论
        top_comments = obj.score_comments.annotate(like_count=Count('score_comment_liked_by')).order_by('-like_count')[:3]
        # 序列化前三条评论
        serializer = ScoreCommentSerializer(instance=top_comments, many=True, context=self.context)
        return serializer.data
    


class ScoreTargetAnyUserSerializer(serializers.ModelSerializer):
    creater = UserSerializer()
    hashtag = HashTagSerializer()
    score_comments = serializers.SerializerMethodField()

    class Meta:
        model = ScoreTarget
        fields = '__all__'

    def get_score_comments(self, obj):
        # 获取热度最高的前三条评论
        top_comments = obj.score_comments.annotate(like_count=Count('score_comment_liked_by')).order_by('-like_count')[:3]
        # 序列化前三条评论
        serializer = ScoreCommentAnyUserSerializer(instance=top_comments, many=True, context=self.context)
        return serializer.data

class ScoreTargetDetailSerializer(serializers.ModelSerializer):
    creater = UserSerializer()
    hashtag = HashTagSerializer()
    score_comments = serializers.SerializerMethodField()

    class Meta:
        model = ScoreTarget
        fields = '__all__'

    def get_score_comments(self, obj):
        # 获取并排序评论
        score_comments = obj.score_comments.annotate(like_count=Count('score_comment_liked_by')).order_by('-like_count', '-created_at')
        serializer = ScoreCommentSerializer(instance=score_comments, many=True, context=self.context)
        return serializer.data
    
class CheckInSerializer(serializers.ModelSerializer):
    consecutive_days = serializers.SerializerMethodField()
    rank = serializers.SerializerMethodField()
    luck_text = serializers.SerializerMethodField()
    
    class Meta:
        model = CheckIn
        fields = ['user', 'date', 'consecutive_days', 'rank','luck','luck_text']

    def get_luck_text(self, obj):
        return obj.get_luck_display()
    
    def get_consecutive_days(self, obj):
        cheindays = obj.user.cheindays
        return cheindays

    def get_rank(self, obj):
        # user_consecutive_days = self.get_consecutive_days(obj)
        chein_rank = WXuser.objects.filter(cheindays__gt=obj.user.cheindays).count() + 1
        return chein_rank
    
class CheckInStandardSerializer(serializers.ModelSerializer):
    luck_text = serializers.SerializerMethodField()

    def get_luck_text(self, obj):
        return obj.get_luck_display()   
     
    class Meta:
        model = CheckIn
        fields = ['date','luck_text']
        
class BambooRankSerializer(serializers.Serializer):
    bamboo = serializers.IntegerField()
    rank = serializers.IntegerField()
    

class FreeBookSerializer(serializers.ModelSerializer):
    price = serializers.SerializerMethodField()

    def get_price(self, obj):
        return obj.get_bamboo_price_display()   
     

    class Meta:
        model = FreeBook
        fields = ['id', 'book_name', 'price', 'profession']  # 包含新增的字段
        
class FreeBookLinkSerializer(serializers.ModelSerializer):

    
    class Meta:
        model = FreeBook
        fields = ['book_link']
        
class NotificationSerializer(serializers.ModelSerializer):
    replyuser = UserSerializer()
    user = UserSerializer()
    
    class Meta:
        model = Notification
        fields = ['id', 'replyuser','user', 'notification_type', 'content', 'related_object_id', 'created_at', 'is_read']

    def update(self, instance, validated_data):
        instance.is_read = validated_data.get('is_read', instance.is_read)
        instance.save()
        return instance

class GroupAllNumerializer(serializers.ModelSerializer):
    
        class Meta:
            model = GroupAllNum
            fields = '__all__'
        
class ActSubCommentSerializer(serializers.ModelSerializer):
    commenter = UserSerializer()
    replyUser = UserSerializer()

    class Meta:
        model = ActSubComment
        fields = '__all__'

class ActCommentSerializer(serializers.ModelSerializer):
    commenter = UserSerializer()
    actsubComments = ActSubCommentSerializer(many=True)

    class Meta:
        model = ActComment
        fields = '__all__'
        
class GroupActivitySerializer(serializers.ModelSerializer):
    creater = UserSerializer()
    participants = UserSerializer(many = True)
    actcomments = ActCommentSerializer(many = True)
    
    class Meta:
        model = GroupActivity
        fields = '__all__'
        
class WechatGroupQrcodeSerializer(serializers.ModelSerializer):
    image_url = serializers.SerializerMethodField()

    class Meta:
        model = WechatGroupQrcode
        fields = ['image_url', 'updated_at','expired_at','is_expired', 'will_expire_soon']
        read_only_fields = ['updated_at','expired_at']

    def get_image_url(self, obj):
        """返回完整的图片URL"""
        return f'{MYHOST}{obj.image.url}'

class LotteryActivitySerializer(serializers.ModelSerializer):

    class Meta:
        model = LotteryActivity
        fields = '__all__'

 
class LotteryActivityRecordSerializer(serializers.ModelSerializer):
    prize_text = serializers.SerializerMethodField()

    class Meta:
        model = LotteryActivityRecord
        fields = ['prize', 'prize_text', 'date']

    def get_prize_text(self, obj):
        return obj.get_prize_display()
