# blog/tasks.py
from celery import shared_task
import time as tm
from .models import *
from .serializers import *
from . import chat

@shared_task
def update_user_authorization(user_id):
    try:
        user = WXuser.objects.get(id=user_id)
        user.isauthorized = True
        user.save()
        print(f"User {user_id} authorized")
    except WXuser.DoesNotExist:
        print(f"User {user_id} does not exist")
        
@shared_task
def is_recommend(content,postid):
    # import ipdb; ipdb.set_trace()
    print("进入@shared_task的is_recommend函数")
    print("content:",content)
    postclass , is_recommend = chat.recommendAPI(content)
    # print("分类及建议",recommendres)
    if postclass in ['5', '6', '7', '8'] or is_recommend == 0:
        ifrecommend_value = False
    else:
        ifrecommend_value = True
    # 保存 post 对象的更改
       # 使用 update 仅更新 `ifrecommend` 字段
    Post.objects.filter(id=postid).update(ifrecommend=ifrecommend_value)
    
    from .views import send_report_email
    if ifrecommend_value == False:
        # 调用发送邮件功能
        # print("发送邮件")
        send_report_email(postid)

    return

@shared_task
def is_activity_recommend(content,actid):

    actclass , is_recommend = chat.actrecommendAPI(content)
    # print("分类及建议",recommendres)
    print(actclass , is_recommend)
    if actclass in ['1', '2', '3', '4'] or is_recommend == 0:
        ifrecommend_value = False
    else:
        ifrecommend_value = True
    # 保存 post 对象的更改
       # 使用 update 仅更新 `ifrecommend` 字段
    GroupActivity.objects.filter(id=actid).update(ifrecommend=ifrecommend_value)
    
    from .views import send_act_report_email
    if ifrecommend_value == False:
        # 调用发送邮件功能
        send_act_report_email(actid)

    return