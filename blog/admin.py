from django.contrib import admin
from .models import *
# Register your models here.
class WXuserAdmin(admin.ModelAdmin):
    # 设置可以搜索的字段
    search_fields = ['nickname']  # 假设你的模型中有一个 'nickname' 字段

# 注册模型和自定义的Admin类
admin.site.register(WXuser, WXuserAdmin)

class PostAdmin(admin.ModelAdmin):
    # 显示哪些字段
    list_display = ('poster', 'content', 'created_at', 'read_count', 'point_count', 'iftop', 'ifrecommend')
    
    # 按用户过滤帖子
    list_filter = ('poster',)  # 添加 'poster' 字段作为过滤器
    
    # 搜索功能
    search_fields = ['poster__nickname', 'poster__wxuser__username', 'content']  # 根据用户名和昵称搜索帖子

# 注册 Post 模型
admin.site.register(Post, PostAdmin)

admin.site.register(PostHashTag)
admin.site.register(Comment)
admin.site.register(SubComment)
admin.site.register(HashTag)
admin.site.register(ScoreTarget)
admin.site.register(ScoreComment)
admin.site.register(ScoreCommentLike)
admin.site.register(ScoreSubComment)
admin.site.register(ScoreSubCommentLike)
admin.site.register(CheckIn)
admin.site.register(FreeBook)
admin.site.register(Lottery)
admin.site.register(Notification)
admin.site.register(GroupActivity)
admin.site.register(ActComment)
admin.site.register(ActSubComment)
admin.site.register(LotteryActivity)
# admin.site.register(LotteryActivityRecord)


@admin.register(WechatGroupQrcode)
class WechatGroupQrcodeAdmin(admin.ModelAdmin):
    """自定义微信群聊二维码的Admin界面"""
    list_display = ['expired_at', 'updated_at',
                   'is_expired', 'will_expire_soon']
    readonly_fields = ['expired_at', 'updated_at', 'is_expired', 'will_expire_soon']
    
    fieldsets = (
        ('二维码信息', {
            'fields': ('image',)
        }),
        ('状态信息', {
            'fields': ('expired_at', 'updated_at', 'is_expired', 'will_expire_soon')
        }),
    )
