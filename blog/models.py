# posts/models.py
from django.db import models
from django.contrib.auth.models import User
from django.db.models.signals import post_save,pre_save
from django.dispatch import receiver
from django.utils import timezone
from datetime import timedelta
import uuid
import os
from django.conf import settings

class WXuser(models.Model):
    wxuser = models.OneToOneField(User, on_delete=models.CASCADE, related_name='wxuser')
    nickname = models.CharField(max_length=10, default="0岁小熊猫")
    randomname = models.CharField(max_length=10, default="沉默的苏格拉底")
    avatar = models.ImageField(upload_to='avatars/', default="avatars/smile.png")
    openid = models.CharField(max_length=100, default="null")
    card = models.ImageField(upload_to='card/', null=True, blank=True)
    isauthorized = models.BooleanField(default=False, verbose_name='是否学生认证')
    timetable = models.ImageField(upload_to='timetable/', null=True, blank=True)
    bamboo = models.IntegerField(default=50, verbose_name='小竹笋')
    is_banned = models.BooleanField(default=False, verbose_name='是否封号')
    banned_until = models.DateTimeField(null=True, blank=True, verbose_name='封禁结束时间')
    cheindays = models.IntegerField(default=0, verbose_name='连续签到天数')
    
    def __str__(self):
        return self.nickname

    def ban(self, duration):
        """Ban the user for a specific duration in days."""
        self.is_banned = True
        self.banned_until = timezone.now() + timedelta(days=duration)
        self.save()

    def unban(self):
        """Unban the user."""
        self.is_banned = False
        self.banned_until = None
        self.save()

    def check_and_unban(self):
        """Check if the ban should be lifted."""
        if self.is_banned and self.banned_until and self.banned_until <= timezone.now():
            self.unban()

class PostHashTag(models.Model):
    hashtag = models.CharField(max_length=10)
    
    def __str__(self):
        return self.hashtag    

class Post(models.Model):
    poster = models.ForeignKey(WXuser, on_delete=models.CASCADE, related_name='posts', verbose_name="发帖人")
    created_at = models.DateTimeField(auto_now_add=True,verbose_name="创建时间")
    content = models.TextField()
    image = models.FileField(upload_to='post_images/', null=True, blank=True,verbose_name="缩略图")
    imagedetail = models.FileField(upload_to='post_images/detail', null=True, blank=True, verbose_name="原图")
    post_hashtag = models.ForeignKey(PostHashTag, on_delete=models.CASCADE, related_name='posts', verbose_name="类别" ,null=True, blank=True, default=None)
    read_count = models.IntegerField(default=0, verbose_name='阅读量')
    point_count = models.IntegerField(default=0, verbose_name='点击量')
    iftop = models.BooleanField(default=False,verbose_name='是否置顶')
    ifrecommend = models.BooleanField(default=True,verbose_name='是否适合展示')
    
    def __str__(self):
        return self.content


class Comment(models.Model):
    commenter = models.ForeignKey(WXuser, on_delete=models.CASCADE, related_name='comments_by_commenter')
    content = models.TextField(max_length=100)
    created_at = models.DateTimeField(auto_now_add=True)
    post = models.ForeignKey(Post, on_delete=models.CASCADE, related_name='comments')

    def __str__(self):
        return f"{self.content}-{self.post.content}"


class SubComment(models.Model):
    comment = models.ForeignKey(Comment, on_delete=models.CASCADE, related_name='subComments')
    commenter = models.ForeignKey(WXuser, on_delete=models.CASCADE, related_name='subComments')
    content = models.TextField(max_length=100)
    created_at = models.DateTimeField(auto_now_add=True)
    replyUser = models.ForeignKey(WXuser, null=True, blank=True, default=None, on_delete=models.CASCADE)
    
    def __str__(self):
        return f"{self.content}-{self.comment.content}"

class Like(models.Model):
    user = models.ForeignKey(WXuser, on_delete=models.CASCADE, related_name='likes')
    post = models.ForeignKey(Post, on_delete=models.CASCADE, related_name='liked_by')
    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        unique_together = ['user', 'post']

    def __str__(self):
        return f"{self.user.nickname} likes {self.post.content}"


class CommentLike(models.Model):
    user = models.ForeignKey(WXuser, on_delete=models.CASCADE, related_name='Commentlikes')
    comment = models.ForeignKey(Comment, on_delete=models.CASCADE, related_name='liked_by')
    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        unique_together = ['user', 'comment']

    def __str__(self):
        return f"{self.user.nickname} likes {self.comment.content}"

class HashTag(models.Model):
    hashtag = models.CharField(max_length=10)
    
    def __str__(self):
        return self.hashtag
    
class ScoreTarget(models.Model):
    creater = models.ForeignKey(WXuser, on_delete=models.CASCADE, related_name='scoreTarget', verbose_name="score-target-creater")
    created_at = models.DateTimeField(auto_now_add=True)
    title = models.TextField()
    score = models.FloatField(default=5)
    image = models.FileField(upload_to='score_target_image/', null=True, blank=True,verbose_name="缩略图")
    imagedetail = models.FileField(upload_to='score_target_image/detail', null=True, blank=True,verbose_name="原图")
    read_count = models.IntegerField(default=0, verbose_name='阅读量')
    hashtag = models.ForeignKey(HashTag, on_delete=models.CASCADE, related_name='scoreTarget')
    
    def __str__(self):
        return self.title
    
class ScoreComment(models.Model):
    user  = models.ForeignKey(WXuser, verbose_name="评分人", on_delete=models.CASCADE)
    score_target = models.ForeignKey(ScoreTarget,verbose_name="打分目标", on_delete=models.CASCADE,related_name='score_comments')
    score = models.FloatField()    
    content = models.TextField(max_length=100)
    image = models.FileField(upload_to='score_target_comment_image/', null=True, blank=True)
    created_at = models.DateTimeField(auto_now_add=True)
    
    def __str__(self):
       return f"{self.user.nickname} score {self.score_target.title}"
    
class ScoreSubComment(models.Model):
    score_comment = models.ForeignKey(ScoreComment, on_delete=models.CASCADE, related_name='scoreSubComments')
    commenter = models.ForeignKey(WXuser, on_delete=models.CASCADE, related_name='scoreSubComments')
    content = models.TextField(max_length=100)
    created_at = models.DateTimeField(auto_now_add=True)
    replyUser = models.ForeignKey(WXuser, null=True, blank=True, default=None, on_delete=models.CASCADE)    
    def __str__(self):
       return f"{self.commenter.nickname} comment {self.score_comment.content}"
      
class ScoreCommentLike(models.Model):
    user = models.ForeignKey(WXuser, on_delete=models.CASCADE, related_name='ScoreCommentLike')
    score_comment = models.ForeignKey(ScoreComment, on_delete=models.CASCADE, related_name='score_comment_liked_by')
    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        unique_together = ['user', 'score_comment']

    def __str__(self):
        return f"{self.user.nickname} likes {self.score_comment.content}"
    
class ScoreSubCommentLike(models.Model):
    user = models.ForeignKey(WXuser, on_delete=models.CASCADE, related_name='ScoreSubCommentLike')
    subcomment = models.ForeignKey(ScoreSubComment, on_delete=models.CASCADE, related_name='liked_by')
    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        unique_together = ['user', 'subcomment']

    def __str__(self):
        return f"{self.user.nickname} likes {self.subcomment.content}"


class CheckIn(models.Model):
    LUCKS_CHOICES = [
        (1, '大吉'),
        (2, '中吉'),
        (3, '小吉'),
        (4, '吉'),
        (5, '中平'),
        (6, '小凶'),
        (7, '半凶'),
        (8, '未吉'),
        (9, '大凶'),
    ]
    
    user = models.ForeignKey(WXuser, on_delete=models.CASCADE)
    date = models.DateField(auto_now_add=True)
    luck = models.IntegerField(choices=LUCKS_CHOICES, default=4)  # 默认为 "吉"

    class Meta:
        unique_together = ('user', 'date')
        
    def __str__(self):
        return f"{self.user.nickname}  {self.date}"    


class FreeBook(models.Model):
    PRICE_CHOICES = [
        (40, '40'),
        (60, '60'),
        (80, '80'),
    ]
    
    book_name = models.TextField()
    book_link = models.URLField(max_length=200, blank=True, null=True)
    bamboo_price = models.IntegerField(choices=PRICE_CHOICES, default=2)
    profession = models.BooleanField(default=False)

        
    def __str__(self):
        return f"{self.book_name}"  
    
class Lottery(models.Model):
    
    user = models.ForeignKey(WXuser, on_delete=models.CASCADE)
    date = models.DateField(auto_now_add=True)

        
    def __str__(self):
        return f"{self.user.nickname}  {self.date}"    

class GroupActivity(models.Model):
    TAG_CHOICES = [
        ('学习', '学习'),
        ('拼车', '拼车'),
        ('美食', '美食'),
        ('拼单', '拼单'),
        ('运动', '运动'),
        ('游戏', '游戏'),
        ('旅行', '旅行'),
        ('音乐', '音乐'),
        ('家教', '家教'),
        ('其他', '其他')
    ]
    creater = models.ForeignKey(
        WXuser, on_delete=models.CASCADE, related_name='created_activities', verbose_name="发起人"
    )
    activity_category = models.CharField(choices=TAG_CHOICES,max_length=10,default='其他')
    content = models.TextField(verbose_name="活动描述")
    required_num = models.PositiveIntegerField(verbose_name="需要人数")
    contact_info = models.CharField(max_length=100, verbose_name="联系方式")
    created_at = models.DateTimeField(auto_now_add=True, verbose_name="创建时间")
    participants = models.ManyToManyField(
        WXuser, related_name='joined_activities', verbose_name="参与者", blank=True
    )
    image = models.FileField(upload_to='act_images/', null=True, blank=True,verbose_name="搭子图")
    location = models.JSONField(verbose_name="位置", null=True, blank=True)
    isover = models.BooleanField(default=False,verbose_name="是否结束")
    ifrecommend = models.BooleanField(default=False,verbose_name="是否推荐")
    
    def __str__(self):
        return f"{self.activity_category} - {self.creater.nickname}"

    def is_full(self):
        """检查活动是否已满员"""
        return self.participants.count() >= self.required_people - 1 

class GroupAllNum(models.Model):
    total_num = models.IntegerField(default=0,verbose_name="总搭子人数")


class Notification(models.Model):
    NOTIFICATION_TYPE_CHOICES = [
        ('postlike', 'postlike'),
        ('commentlike', 'commentlike'),
        ('comment', 'Comment'),
        ('subcomment', 'subcomment'),
        ('targetcommentlike','targetcommentlike'),
        ('targetsubcommentlike','targetsubcommentlike'),
        ('targetcomment','targetcomment'),
        ('targetsubcomment','targetsubcomment'),
        ('mention', 'Mention'),
        ('actjoin','actjoin'),
        ('actcomment','actcomment'),
        ('subactcomment','subactcomment')
    ]
    replyuser = models.ForeignKey(WXuser, on_delete=models.CASCADE, related_name='do_comments_or_praise')
    user = models.ForeignKey(WXuser, on_delete=models.CASCADE, related_name='notifications')
    notification_type = models.CharField(max_length=20, choices=NOTIFICATION_TYPE_CHOICES)
    content = models.TextField()
    related_object_id = models.PositiveIntegerField()  # 可以是评论ID、私信ID等
    created_at = models.DateTimeField(auto_now_add=True)
    is_read = models.BooleanField(default=False)

    def __str__(self):
        return f"Notification for {self.user.nickname}: {self.content[:20]}"

    def mark_as_read(self):
        self.is_read = True
        self.save()
        
class ActComment(models.Model):
    commenter = models.ForeignKey(WXuser, on_delete=models.CASCADE, related_name='actcomments_by_commenter')
    content = models.TextField(max_length=100)
    created_at = models.DateTimeField(auto_now_add=True)
    activity = models.ForeignKey(GroupActivity, on_delete=models.CASCADE, related_name='actcomments')

    def __str__(self):
        return f"{self.content}-{self.activity.content}"


class ActSubComment(models.Model):
    actcomment = models.ForeignKey(ActComment, on_delete=models.CASCADE, related_name='actsubComments')
    commenter = models.ForeignKey(WXuser, on_delete=models.CASCADE, related_name='actsubComments')
    content = models.TextField(max_length=100)
    created_at = models.DateTimeField(auto_now_add=True)
    replyUser = models.ForeignKey(WXuser, null=True, blank=True, default=None, on_delete=models.CASCADE)
    
    def __str__(self):
        return f"{self.content}-{self.actcomment.content}"



class WechatGroupQrcode(models.Model):
    """微信群聊二维码模型（确保系统中始终只有且仅有一个有效二维码）"""
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    image = models.ImageField(upload_to='wechat_qrcodes/', verbose_name="二维码图片")
    updated_at = models.DateTimeField(auto_now=True, verbose_name="更新时间")
    expired_at = models.DateTimeField(null=True, blank=True, verbose_name="过期时间")

    class Meta:
        verbose_name = "微信群聊二维码"
        verbose_name_plural = "微信群聊二维码"

    def __str__(self):
        return f"二维码"

    def clean(self):
        # 确保系统中最多只有一个记录
        if not self.pk and WechatGroupQrcode.objects.exists():
            raise Exception("系统中只能有一个微信群聊二维码，请更新现有二维码而不是创建新的")

    def save(self, *args, **kwargs):
        # 如果是更新操作，删除旧文件
        if self.pk:
            try:
                old_instance = WechatGroupQrcode.objects.get(pk=self.pk)
                if old_instance.image and old_instance.image != self.image:
                    # 删除旧文件
                    old_image_path = os.path.join(settings.MEDIA_ROOT, old_instance.image.name)
                    if os.path.exists(old_image_path):
                        os.remove(old_image_path)
            except WechatGroupQrcode.DoesNotExist:
                pass

        # 确保文件名一致（例如固定为 'qrcode.png'）
        if self.image:
            self.image.name = 'qrcode.png'  # 固定文件名

        # 自动设置过期时间为7天后
        self.expired_at = timezone.now() + timedelta(days=7)
        self.full_clean()  # 调用验证
        super().save(*args, **kwargs)

    @property
    def is_expired(self):
        """判断二维码是否过期（7天有效期）"""
        if not self.expired_at:
            return True
        days_passed = (timezone.now() - self.updated_at).days
        return days_passed >= 7

    @property
    def will_expire_soon(self):
        """判断二维码是否即将过期（6天以上，接近7天）"""
        if not self.updated_at:
            return True
        days_passed = (timezone.now() - self.updated_at).days
        return 6 <= days_passed < 7

class LotteryActivity(models.Model):
    name = models.CharField(max_length=50, unique=True, verbose_name="活动名称")
    description = models.TextField(verbose_name="活动描述")
    start_time = models.DateTimeField(verbose_name="开始时间")
    end_time = models.DateTimeField(verbose_name="结束时间")
    prob_first = models.FloatField(default=0.05, verbose_name="一等奖概率")
    prob_second = models.FloatField(default=0.1, verbose_name="二等奖概率")
    prob_third = models.FloatField(default=0.15, verbose_name="三等奖概率")
    is_active = models.BooleanField(default=True, verbose_name="是否开启")
    
    class Meta:
        verbose_name = "抽奖活动"
        verbose_name_plural = "抽奖活动"
        
    def __str__(self):
        return self.name

    def is_running(self):
        now = timezone.now()
        return self.is_active and self.start_time <= now <= self.end_time
    
    def clean(self):
        total = self.prob_first + self.prob_second + self.prob_third
        if total > 1:
            raise Exception("总概率不能大于 1")

class LotteryActivityRecord(models.Model):
    PRIZE_CHOICES = [
        (1, '一等奖'),
        (2, '二等奖'),
        (3, '三等奖'),
        (0, '谢谢参与'),
    ]

    user = models.ForeignKey(WXuser, on_delete=models.CASCADE)
    activity = models.ForeignKey(LotteryActivity, on_delete=models.CASCADE)
    prize = models.IntegerField(choices=PRIZE_CHOICES, default=0)
    date = models.DateTimeField(auto_now_add=True)

    class Meta:
        unique_together = ('user', 'activity')

    def __str__(self):
        return f"{self.user.nickname} - {self.activity.name} - {self.get_prize_display()}"
