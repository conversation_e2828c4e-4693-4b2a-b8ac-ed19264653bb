from django.contrib import admin
from django.urls import path, include, re_path
from django.views.static import serve
from .settings import STATICFILES_DIRS,MEDIA_ROOT
from django.shortcuts import redirect

urlpatterns = [
    path('', lambda request: redirect('page/', permanent=True)),
    path('page/', include('webpages.urls'),name='page'),
    path('admin/', admin.site.urls),
    path('api-auth/', include('rest_framework.urls')),
    path('api/blog/', include("blog.urls"), name='blog'),
    path('gzh/', include("gzh.urls"), name='gzh'),

    # 在开发环境中提供静态文件
    re_path(r'^media/(?P<path>.*)$', serve, {'document_root': MEDIA_ROOT}),
]
