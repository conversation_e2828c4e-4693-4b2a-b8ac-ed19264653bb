# myproject/celery.py
from __future__ import absolute_import, unicode_literals
import os
from celery import Celery

# 设置环境变量，告诉 Django 使用哪些设置模块
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'miniprogram.settings')

# 创建 Celery 实例
app = Celery('miniprogram')

# 使用 Django 的 settings 配置 Celery，namespace='CELERY' 表示以 CELERY_ 开头的变量为 Celery 配置
app.config_from_object('django.conf:settings', namespace='CELERY')

# 自动从所有已注册的 Django app 里加载任务模块
app.autodiscover_tasks()

@app.task(bind=True)
def debug_task(self):
    print(f'Request: {self.request!r}')
