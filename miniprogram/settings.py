"""
Django settings for miniprogram project.

Generated by 'django-admin startproject' using Django 4.2.

For more information on this file, see
https://docs.djangoproject.com/en/4.2/topics/settings/

For the full list of settings and their values, see
https://docs.djangoproject.com/en/4.2/ref/settings/
"""

# 使用 Redis 作为 Celery 的消息代理
CELERY_BROKER_URL = 'redis://localhost:6379/0'

# 结果后端存储
CELERY_RESULT_BACKEND = 'redis://localhost:6379/0'

CELERY_BROKER_CONNECTION_RETRY_ON_STARTUP = True
# 时区配置
CELERY_TIMEZONE = 'Asia/Shanghai'
CELERY_TASK_TRACK_STARTED = True
CELERY_TASK_TIME_LIMIT =  60  # 设置任务的最大运行时间为 30 分钟




from pathlib import Path
import os

# Build paths inside the project like this: BASE_DIR / 'subdir'.
BASE_DIR = Path(__file__).resolve().parent.parent


# Quick-start development settings - unsuitable for production
# See https://docs.djangoproject.com/en/4.2/howto/deployment/checklist/

# SECURITY WARNING: keep the secret key used in production secret!
SECRET_KEY = 'django-insecure-^=i=y@@gpa#3&wg=g+#zw15y7riojt@lr#-eniz2f%(k=9qhd%'

# SECURITY WARNING: don't run with debug turned on in production!

########### 实战 #############################################################
# DEBUG =   False 
# MYHOST = 'https://scuclub.top'
# USE_X_FORWARDED_HOST = True
# SECURE_PROXY_SSL_HEADER = ('HTTP_X_FORWARDED_PROTO', 'https')

# # Database
# # https://docs.djangoproject.com/en/4.2/ref/settings/#databases

# DATABASES = {
#     'default': {
#         'ENGINE': 'django.db.backends.mysql',
#         'NAME': os.environ.get('DJANGO_MYSQL_DATABASE') or 'miniprogram',
#         'USER': os.environ.get('DJANGO_MYSQL_USER') or 'django_user',
#         'PASSWORD': os.environ.get('DJANGO_MYSQL_PASSWORD') or 'password1',
#         'HOST': os.environ.get('DJANGO_MYSQL_HOST') or '********',
#         'PORT': int(
#             os.environ.get('DJANGO_MYSQL_PORT') or 3306),
#         'OPTIONS': {'charset':'utf8mb4'},  
#     }}
###############################################################################


########### 开发 ##############################################################
DEBUG = True
MYHOST = 'http://127.0.0.1:8000'

##mac local database

DATABASES = {
    'default': {
        'ENGINE': 'django.db.backends.mysql',
        'NAME': os.environ.get('DJANGO_MYSQL_DATABASE') or 'miniprogram',
        'USER': os.environ.get('DJANGO_MYSQL_USER') or 'root',
        'PASSWORD': os.environ.get('DJANGO_MYSQL_PASSWORD') or '',
        'HOST': os.environ.get('DJANGO_MYSQL_HOST') or '127.0.0.1',
        'PORT': int(
            os.environ.get('DJANGO_MYSQL_PORT') or 3306),
        'OPTIONS': {'charset':'utf8mb4'},  
    }}
###############################################################################


APPID = "wx179988572d61c170"
APPSECRET = "6d79d11454c99487a845a311072eb9ed"

GZHAPPID ='wxc1c28f900f0851d9'
GZHAPPSECRET ='6a8fdc944497074b9be9cb978bd870ab'

AUTH_AUTO_TIME = 60 #自动认证时间，60s

# ALLOWED_HOSTS = ['*']
ALLOWED_HOSTS = ['scuclub.top','www.scuclub.top','localhost','127.0.0.1']

# Application definition

INSTALLED_APPS = [
    # "daphne",
    'django_crontab',
    'django.contrib.admin',
    'django.contrib.auth',
    'django.contrib.contenttypes',
    'django.contrib.sessions',
    'django.contrib.messages',
    'django.contrib.staticfiles',
    "blog.apps.BlogConfig",
    "gzh",
    "webpages.apps.WebpagesConfig",
    'rest_framework',
]

MIDDLEWARE = [
    'django.middleware.security.SecurityMiddleware',
    'django.contrib.sessions.middleware.SessionMiddleware',
    'django.middleware.common.CommonMiddleware',
    'django.middleware.csrf.CsrfViewMiddleware',
    'django.contrib.auth.middleware.AuthenticationMiddleware',
    'django.contrib.messages.middleware.MessageMiddleware',
    'django.middleware.clickjacking.XFrameOptionsMiddleware',
]

CSRF_TRUSTED_ORIGINS=['https://scuclub.top']

ROOT_URLCONF = 'miniprogram.urls'

TEMPLATES = [
    {
        'BACKEND': 'django.template.backends.django.DjangoTemplates',
        'DIRS': [],
        'APP_DIRS': True,
        'OPTIONS': {
            'context_processors': [
                'django.template.context_processors.debug',
                'django.template.context_processors.request',
                'django.contrib.auth.context_processors.auth',
                'django.contrib.messages.context_processors.messages',
            ],
        },
    },
]

ASGI_APPLICATION = 'miniprogram.asgi.application'


# Password validation
# https://docs.djangoproject.com/en/4.2/ref/settings/#auth-password-validators

AUTH_PASSWORD_VALIDATORS = [
    {
        'NAME': 'django.contrib.auth.password_validation.UserAttributeSimilarityValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.MinimumLengthValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.CommonPasswordValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.NumericPasswordValidator',
    },
]


# Internationalization
# https://docs.djangoproject.com/en/4.2/topics/i18n/

LANGUAGE_CODE = 'zh-hans'

TIME_ZONE = 'Asia/Shanghai'

USE_I18N = True

USE_TZ = True


# Default primary key field type
# https://docs.djangoproject.com/en/4.2/ref/settings/#default-auto-field

DEFAULT_AUTO_FIELD = 'django.db.models.BigAutoField'

REST_FRAMEWORK = {

    'DEFAULT_AUTHENTICATION_CLASSES': (
        'rest_framework_simplejwt.authentication.JWTAuthentication',
        'rest_framework.authentication.BasicAuthentication',
    ),
    'DEFAULT_PERMISSION_CLASSES': (
        'rest_framework.permissions.AllowAny',
    )
}
from datetime import timedelta
SIMPLE_JWT = {
    'ACCESS_TOKEN_LIFETIME': timedelta(days=7),  # 配置过期时间
    'REFRESH_TOKEN_LIFETIME': timedelta(days=15),
}
# Static files (CSS, JavaScript, Images)
# https://docs.djangoproject.com/en/4.2/howto/static-files/

STATIC_URL = '/static/'
STATIC_ROOT = os.path.join(BASE_DIR, 'staticfiles')  # 静态文件收集目录
STATICFILES_DIRS = (
    os.path.join(BASE_DIR, 'static'),
)

MEDIA_ROOT = os.path.join(BASE_DIR, 'media')
MEDIA_URL = '/media/'

# 定时任务
CRONJOBS  = [
    ('0 5 * * *', 'blog.cron.DeleteNightPostsCronJob','>>/home/<USER>/miniprogram/delete_midnight_posts.log'),
    # ('*/10 * * * *', 'blog.cron.fetch_and_process_posts','>>/home/<USER>/miniprogram/generate_short_url.log')
]

# 发邮件设置

EMAIL_BACKEND = 'django.core.mail.backends.smtp.EmailBackend'
EMAIL_HOST = 'smtp.163.com'  # 163邮箱的SMTP服务器地址
EMAIL_PORT = 465  # SMTP服务器端口，163邮箱通常使用465端口
EMAIL_USE_TLS = False  # 不使用TLS
EMAIL_USE_SSL = True  # 使用SSL加密
EMAIL_HOST_USER = '<EMAIL>'  # 发件人邮箱
EMAIL_HOST_PASSWORD = 'JBYMWUEESGHATDSU'  # 发件人邮箱密码
DEFAULT_FROM_EMAIL = '<EMAIL>'  # 默认发件人邮箱
EMAIL_TO_LIST = ['<EMAIL>']  # 接收通知的邮箱列表

# 缓存设置
CACHES = {
    'default': {
        'BACKEND': 'django_redis.cache.RedisCache',
        'LOCATION': 'redis://127.0.0.1:6379/1',  # Redis服务器的地址和端口
        'OPTIONS': {
            'CLIENT_CLASS': 'django_redis.client.DefaultClient',
        }
    }
}

LOGS_DIR = 'logs'
Path(LOGS_DIR).mkdir(parents=True, exist_ok=True)

LOGGING = {
    'version': 1,
    'disable_existing_loggers': False,
    'formatters': {
        'standard': {
            'format': '%(asctime)s [%(levelname)s] %(name)s: %(message)s',
            'datefmt': '%Y-%m-%d %H:%M:%S',
        },
    },
    'handlers': {
        # 1. 请求日志处理器（写入 requests.log）
        'request_handler': {
            'level': 'INFO',
            'class': 'logging.FileHandler',
            'filename': 'logs/requests.log',
            'formatter': 'standard',
            'encoding': 'utf-8',
        },
        # 2. 数据库日志处理器（写入 database.log）
        'database_handler': {
            'level': 'INFO',  # 仅开发环境记录 DEBUG，生产环境可设为 INFO
            'class': 'logging.FileHandler',
            'filename': 'logs/database.log',
            'formatter': 'standard',
            'encoding': 'utf-8',
        },
        # 3. 审核日志处理器（写入 audit.log）
        'audit_handler': {
            'level': 'INFO',
            'class': 'logging.FileHandler',
            'filename': 'logs/audit.log',
            'formatter': 'standard',
            'encoding': 'utf-8',
        },
        # 4. 错误日志处理器（写入 errors.log）
        'error_handler': {
            'level': 'ERROR',
            'class': 'logging.FileHandler',
            'filename': 'logs/errors.log',
            'formatter': 'standard',
            'encoding': 'utf-8',
        },
        # 5. 控制台处理器（开发环境输出到终端）
        'console': {
            'level': 'DEBUG',
            'class': 'logging.StreamHandler',
            'formatter': 'standard',
        },
    },
    'loggers': {
        # 1. Django 请求日志（仅记录请求相关事件）
        'django.request': {
            'handlers': ['request_handler', 'console'],  # 开发环境输出到控制台，生产环境可移除 console
            'level': 'INFO',
            'propagate': False,  # 禁止向上传播到根日志器
        },
        # 2. 数据库操作日志（如 SQL 执行）
        'django.db.backends': {
            'handlers': ['database_handler'],
            'level': 'INFO',
            'propagate': False,
        },
        # 3. 应用审核日志（自定义模块，如 blog.audit）
        'blog.audit': {  # 假设审核功能在 blog 应用中
            'handlers': ['audit_handler'],
            'level': 'INFO',
            'propagate': False,
        },
        # 4. 全局错误日志（捕获所有 ERROR 级别日志）
        '': {  # 根日志器，处理未被特定日志器捕获的日志
            'handlers': ['error_handler', 'console'],
            'level': 'ERROR',
        },
    },
}