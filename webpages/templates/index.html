<!DOCTYPE html>
<html>
<head>
<style>
* {
  box-sizing: border-box;
}

body {
  font-family: Arial;
  padding: 10px;
  background: #f1f1f1;
}

/* 页眉/Blog 标题 */
.header {
  padding: 30px;
  text-align: center;
  background: white;
}

.header h1 {
  font-size: 50px;
}

.header p {
  letter-spacing: 9px;
}

/* 设置上导航栏的样式 */
.topnav {
  overflow: hidden;
  background-color: #333;
}

/* 设置 topnav 链接的样式 */
.topnav a {
  float: left;
  display: block;
  color: #f2f2f2;
  text-align: center;
  padding: 14px 16px;
  text-decoration: none;
}

/* 改变鼠标悬停时的颜色 */
.topnav a:hover {
  background-color: #ddd;
  color: black;
}

/* 创建两个不相等的彼此并排的浮动列 */
/* 左列 */
.leftcolumn {   
  float: left;
  width: 75%;
}

/* 右列 */
.rightcolumn {
  float: left;
  width: 25%;
  background-color: #f1f1f1;
  padding-left: 20px;
}

/* 伪图像 */
.fakeimg {
  background-color: #aaa;
  width: 100%;
  padding: 20px;
}

/* 为文章添加卡片效果 */
.card {
  background-color: white;
  padding: 20px;
  margin-top: 20px;
}

/* 清除列之后的浮动 */
.row:after {
  content: "";
  display: table;
  clear: both;
}

/* 页脚 */
.footer {
  padding: 20px;
  text-align: center;
  background: #ddd;
  margin-top: 20px;
}

/* 响应式布局 - 当屏幕的宽度小于 800 像素时，使两列堆叠而不是并排 */
@media screen and (max-width: 800px) {
  .leftcolumn, .rightcolumn {   
    width: 100%;
    padding: 0;
  }
}

/* 响应式布局 - 当屏幕的宽度小于 400 像素时，使导航链接堆叠而不是并排 */
@media screen and (max-width: 400px) {
  .topnav a {
    float: none;
    width: 100%;
  }
}
</style>
<title>柳岸晓风(成都)网络科技有限公司</title>
</head>

<body>

<div class="header">
  <h1>柳岸晓风(成都)网络科技有限公司</h1>
  <p>我们立志为成都学子提供最优质的本地服务~.本网站仅提供服务器后端的接口服务</p>
</div>


<a href="https://beian.miit.gov.cn/" target="_blank">备案号</a>
<a href="https://beian.miit.gov.cn/" target="_blank">蜀ICP备2023018945号</a>

</body>
</html>
